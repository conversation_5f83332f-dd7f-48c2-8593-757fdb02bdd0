
/**
 * RevolicoScraper.ts
 * Implementación del scraper especializado para Revolico.com
 * Utiliza la API GraphQL de Revolico.com para obtener listados de laptops
 */

import axios from 'axios';
import { ScraperBase } from './scraper-base';
import { LaptopData, ScrapingResult, ScrapingSource, ScrapingSession, ScrapingError } from '../types/scraping.types';
import { Logger } from '../utils/logger';

export class RevolicoScraper extends ScraperBase {
  private logger = new Logger('RevolicoScraper');
  private source: ScrapingSource;
  private session: ScrapingSession;
  private errors: ScrapingError[] = [];

  constructor(source: ScrapingSource) {
    super();
    this.source = source;
    this.session = {
      timestamp: new Date().toISOString(),
      totalProducts: 0,
      errors: []
    };
  }

  /**
   * Ejecuta el scraper de Revolico.com
   * @returns ScrapingResult con los resultados del scraping
   */
  async scrape(): Promise<ScrapingResult> {
    this.logger.info('Iniciando scraping de Revolico.com');
    const laptops: LaptopData[] = [];

    try {
      if (!this.source.selectors || !this.source.selectors.graphql) {
        throw new Error('Configuración de GraphQL no encontrada para Revolico.com');
      }

      const graphqlConfig = this.source.selectors.graphql;
      const { query, variables, pageParam, maxPages, dataMapping } = graphqlConfig;
      let currentPage = 1;
      let hasMorePages = true;

      while (hasMorePages && currentPage <= (maxPages || 1)) {
        const pageVariables = { ...variables, [pageParam]: currentPage };
        
        this.logger.info(`Obteniendo página ${currentPage} de ${maxPages || 1}`);
        
        try {
          const response = await axios.post(this.source.base_url, {
            query,
            variables: pageVariables
          }, {
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            }
          });

          if (response.data.errors) {
            this.logError('Error en la consulta GraphQL', new Error(JSON.stringify(response.data.errors)));
            break;
          }

          const items = this.getNestedValue(response.data, dataMapping.items);
          
          if (!Array.isArray(items) || items.length === 0) {
            this.logger.info(`No se encontraron más resultados en la página ${currentPage}`);
            break;
          }

          // Procesar los items y convertirlos a LaptopData
          const processedItems = this.processItems(items, dataMapping);
          laptops.push(...processedItems);

          // Verificar si hay más páginas
          const pagination = this.getNestedValue(response.data, 'data.search.pagination');
          hasMorePages = pagination && pagination.current_page < pagination.total_pages;
          currentPage++;

          // Agregar un retraso para no sobrecargar la API
          await this.delay(1000);

        } catch (error) {
          this.logError(`Error al procesar la página ${currentPage}`, error);
          // Intentar continuar con la siguiente página a pesar del error
          currentPage++;
          await this.delay(2000); // Mayor retraso después de un error
        }
      }

      this.logger.info(`Scraping completado. Se encontraron ${laptops.length} laptops.`);

    } catch (error) {
      this.logError('Error general en el scraper de Revolico', error);
    }

    // Actualizar la información de la sesión
    this.session.totalProducts = laptops.length;
    
    return {
      laptops,
      session: this.session
    };
  }

  /**
   * Procesa los items devueltos por la API GraphQL y los convierte a LaptopData
   * @param items Items devueltos por la API
   * @param mapping Configuración de mapeo de datos
   * @returns Array de objetos LaptopData
   */
  private processItems(items: any[], mapping: any): LaptopData[] {
    return items.map(item => {
      try {
        // Extraer los datos básicos
        const name = this.getNestedValue(item, mapping.name) || 'Desconocido';
        const description = this.getNestedValue(item, mapping.description) || '';
        const price = parseFloat(this.getNestedValue(item, mapping.price)) || 0;
        const currency = this.getNestedValue(item, mapping.currency) || 'USD';
        const imageUrl = this.getNestedValue(item, mapping.imageUrl) || '';
        const publishDate = this.getNestedValue(item, mapping.publishDate) || new Date().toISOString();

        // Extraer las especificaciones
        const specs = this.extractSpecs(item, mapping.specs);

        // Extraer la información de contacto
        const contactInfo = {
          phone: this.getNestedValue(item, mapping.contactInfo.phone),
          email: this.getNestedValue(item, mapping.contactInfo.email)
        };

        // Generar un ID único para este laptop
        const id = `revolico-${item.id || Date.now().toString(36)}`;

        return {
          id,
          name,
          brand: this.extractBrandFromName(name),
          price,
          specifications: specs,
          inStock: true, // Asumimos que está disponible si está en el listado
          imageUrl,
          description,
          source: {
            name: 'Revolico.com',
            url: `https://www.revolico.com/item/${item.id}`,
            publishDate,
            contactInfo
          },
          currency
        };
      } catch (error) {
        this.logError('Error al procesar un item', error);
        return null;
      }
    }).filter(Boolean) as LaptopData[];
  }

  /**
   * Extrae las especificaciones del laptop de los atributos del anuncio
   * @param item Item del anuncio
   * @param specsConfig Configuración de extracción de especificaciones
   * @returns Objeto con las especificaciones estructuradas
   */
  private extractSpecs(item: any, specsConfig: any): any {
    try {
      const rawSpecs = this.getNestedValue(item, specsConfig.mapping) || {};
      
      // Aplicar transformaciones específicas según la configuración
      const cpu = this.getNestedValue(item, specsConfig.transforms.cpu) || this.extractCpuFromDescription(item.description);
      const ram = this.getNestedValue(item, specsConfig.transforms.ram) || this.extractRamFromDescription(item.description);
      const storage = this.getNestedValue(item, specsConfig.transforms.storage) || this.extractStorageFromDescription(item.description);
      const gpu = this.getNestedValue(item, specsConfig.transforms.gpu) || this.extractGpuFromDescription(item.description);

      return {
        cpu: this.parseCpu(cpu),
        ram: this.parseRam(ram),
        storage: this.parseStorage(storage),
        gpu: this.parseGpu(gpu),
        // Incluir otros atributos que pudieran estar disponibles
        ...rawSpecs
      };
    } catch (error) {
      this.logError('Error al extraer especificaciones', error);
      return {};
    }
  }

  /**
   * Parsea información sobre la CPU a partir de un texto
   */
  private parseCpu(cpuText: string): any {
    if (!cpuText) return { name: 'Desconocido' };
    
    const cpuInfo = {
      name: cpuText,
      cores: undefined,
      threads: undefined,
      baseClockSpeed: undefined
    };

    // Extraer número de núcleos si está disponible
    const coresMatch = cpuText.match(/(\d+)\s*(?:núcleos|cores)/i);
    if (coresMatch) {
      cpuInfo.cores = parseInt(coresMatch[1], 10);
    }

    // Extraer velocidad del reloj si está disponible
    const speedMatch = cpuText.match(/(\d+(?:\.\d+)?)\s*(?:GHz)/i);
    if (speedMatch) {
      cpuInfo.baseClockSpeed = parseFloat(speedMatch[1]);
    }

    return cpuInfo;
  }

  /**
   * Parsea información sobre la RAM a partir de un texto
   */
  private parseRam(ramText: string): any {
    if (!ramText) return { size: 0, type: 'Desconocido' };

    const sizeMatch = ramText.match(/(\d+)\s*(?:GB|G)/i);
    const typeMatch = ramText.match(/DDR[34]/i);

    return {
      size: sizeMatch ? parseInt(sizeMatch[1], 10) : 0,
      type: typeMatch ? typeMatch[0] : 'Desconocido'
    };
  }

  /**
   * Parsea información sobre el almacenamiento a partir de un texto
   */
  private parseStorage(storageText: string): any {
    if (!storageText) return { size: 0, type: 'Desconocido' };

    const ssdMatch = /SSD/i.test(storageText);
    const hddMatch = /HDD/i.test(storageText);
    const sizeMatch = storageText.match(/(\d+)\s*(?:GB|TB|G|T)/i);
    
    let size = 0;
    if (sizeMatch) {
      size = parseInt(sizeMatch[1], 10);
      // Convertir TB a GB si es necesario
      if (sizeMatch[0].toUpperCase().includes('TB') || sizeMatch[0].toUpperCase().includes('T')) {
        size *= 1024;
      }
    }

    return {
      size,
      type: ssdMatch ? 'SSD' : (hddMatch ? 'HDD' : 'Desconocido')
    };
  }

  /**
   * Parsea información sobre la GPU a partir de un texto
   */
  private parseGpu(gpuText: string): any {
    if (!gpuText) return { name: 'Integrada', type: 'integrated' };

    const dedicatedGpuPatterns = [
      /nvidia/i, /geforce/i, /rtx/i, /gtx/i,
      /amd/i, /radeon/i, /rx/i
    ];

    const isDedicated = dedicatedGpuPatterns.some(pattern => pattern.test(gpuText));

    return {
      name: gpuText,
      type: isDedicated ? 'dedicated' : 'integrated'
    };
  }

  /**
   * Intenta extraer la marca del laptop a partir del nombre del anuncio
   */
  private extractBrandFromName(name: string): string {
    const commonBrands = [
      'Dell', 'HP', 'Lenovo', 'Asus', 'Acer', 'Apple', 'MSI', 
      'Samsung', 'Huawei', 'Xiaomi', 'Microsoft', 'Toshiba',
      'LG', 'Razer', 'Alienware', 'Sony', 'Gigabyte'
    ];

    // Buscar si alguna marca conocida aparece en el nombre
    for (const brand of commonBrands) {
      if (name.toLowerCase().includes(brand.toLowerCase())) {
        return brand;
      }
    }

    // Si no se encuentra una marca conocida
    return 'Desconocido';
  }

  /**
   * Extrae información de CPU de la descripción del anuncio
   */
  private extractCpuFromDescription(description: string): string | null {
    if (!description) return null;
    
    // Patrones comunes para CPU
    const cpuPatterns = [
      /(?:intel|i[357]|i[357]-\d{4,5}[A-Z]*|core i[357])[^\n,.]*/i,
      /(?:ryzen|amd)[^\n,.]*/i,
      /(?:processor|cpu|procesador)[^\n,.]*/i
    ];

    for (const pattern of cpuPatterns) {
      const match = description.match(pattern);
      if (match) return match[0].trim();
    }

    return null;
  }

  /**
   * Extrae información de RAM de la descripción del anuncio
   */
  private extractRamFromDescription(description: string): string | null {
    if (!description) return null;
    
    // Patrones para RAM
    const ramPattern = /(?:\d+\s*GB|\d+\s*G)\s*(?:RAM|DDR[34])/i;
    const match = description.match(ramPattern);
    
    return match ? match[0].trim() : null;
  }

  /**
   * Extrae información de almacenamiento de la descripción del anuncio
   */
  private extractStorageFromDescription(description: string): string | null {
    if (!description) return null;
    
    // Patrones para almacenamiento
    const storagePattern = /(?:\d+\s*(?:GB|TB|G|T))\s*(?:SSD|HDD|NVME|almacenamiento|storage)/i;
    const match = description.match(storagePattern);
    
    return match ? match[0].trim() : null;
  }

  /**
   * Extrae información de GPU de la descripción del anuncio
   */
  private extractGpuFromDescription(description: string): string | null {
    if (!description) return null;

    // Patrones para GPU
    const gpuPatterns = [
      /(?:nvidia|geforce)\s*(?:rtx|gtx|mx)?\s*\d{3,4}/i,
      /(?:amd|radeon)\s*(?:rx)?\s*\d{3,4}/i,
      /(?:intel|iris|uhd)\s*(?:graphics|xe)/i
    ];

    for (const pattern of gpuPatterns) {
      const match = description.match(pattern);
      if (match) return match[0].trim();
    }

    return null;
  }

  /**
   * Obtiene un valor anidado de un objeto utilizando una ruta de acceso
   * @param obj Objeto de origen
   * @param path Ruta de acceso (separada por puntos)
   * @returns Valor encontrado o undefined
   */
  private getNestedValue(obj: any, path: string): any {
    if (!obj || !path) return undefined;
    
    // Manejar acceso a arrays con notación de corchetes: items[0].name
    const parts = path.split('.');
    let current = obj;
    
    for (const part of parts) {
      if (part.includes('[') && part.includes(']')) {
        const arrayName = part.substring(0, part.indexOf('['));
        const index = parseInt(part.substring(part.indexOf('[') + 1, part.indexOf(']')), 10);
        
        current = current[arrayName];
        if (!current || !Array.isArray(current) || index >= current.length) {
          return undefined;
        }
        
        current = current[index];
      } else {
        if (current === undefined || current === null) {
          return undefined;
        }
        current = current[part];
      }
    }
    
    return current;
  }

  /**
   * Registra un error en el log y en la sesión
   * @param message Mensaje de error
   * @param error Objeto de error
   */
  private logError(message: string, error: any): void {
    this.logger.error(`${message}: ${error.message || error}`);

    this.errors.push({
      url: this.source.base_url,
      message: error.message || error.toString(),
      stack: error.stack
    });

    this.session.errors = this.errors;
  }

  /**
   * Implementa un retraso para espaciar las peticiones
   * @param ms Milisegundos a esperar
   * @returns Promesa que se resuelve después del tiempo especificado
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
